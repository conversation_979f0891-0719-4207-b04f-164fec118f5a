import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import React from "react"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format user mentions from the backend format to frontend display format
 * Backend format: 「realID @{realID} username @{username} on {platform}」
 * Frontend format:
 * - Telegram: @username on telegram (plain text)
 * - Farcaster: @username on farcaster (with clickable @username that links to fid)
 */
export function formatUserMentions(text: string): string {
  // Regex to match the backend mention format - updated to handle usernames with hyphens
  const mentionRegex = /「realID @([^\s]+) username @([^\s]+) on (\w+)」/g

  return text.replace(mentionRegex, (match, realID, username, platform) => {
    const platformLower = platform.toLowerCase()

    if (platformLower === 'telegram') {
      return `@${username} on telegram`
    } else if (platformLower === 'farcaster') {
      return `@${username} on farcaster`
    } else {
      // Fallback for unknown platforms
      return `@${username} on ${platform}`
    }
  })
}

/**
 * Parse text with mentions and return an array of text segments and mention objects
 * This is used for rendering mentions with interactive elements
 */
export interface MentionSegment {
  type: 'text' | 'mention'
  content: string
  realID?: string
  username?: string
  platform?: string
}

export function parseUserMentions(text: string): MentionSegment[] {
  // Updated regex to handle usernames with hyphens and other characters
  const mentionRegex = /「realID @([^\s]+) username @([^\s]+) on (\w+)」/g
  const segments: MentionSegment[] = []
  let lastIndex = 0
  let match

  while ((match = mentionRegex.exec(text)) !== null) {
    // Add text before the mention
    if (match.index > lastIndex) {
      segments.push({
        type: 'text',
        content: text.slice(lastIndex, match.index)
      })
    }

    // Add the mention segment
    const [, realID, username, platform] = match
    segments.push({
      type: 'mention',
      content: match[0],
      realID,
      username,
      platform: platform.toLowerCase()
    })

    lastIndex = match.index + match[0].length
  }

  // Add remaining text
  if (lastIndex < text.length) {
    segments.push({
      type: 'text',
      content: text.slice(lastIndex)
    })
  }

  return segments
}
